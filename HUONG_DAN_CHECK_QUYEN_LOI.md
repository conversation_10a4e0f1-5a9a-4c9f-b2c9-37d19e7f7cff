# Hướng dẫn sử dụng chức năng "Check quyền lợi" (Đ<PERSON> sửa lỗi)

## 🔧 **Vấn đề đã được khắc phục**
- **Trước đây**: Sheet "Check quyền lợi" chỉ copy header mà không hiển thị dữ liệu ID
- **Bây giờ**: Sheet hiển thị đầy đủ **CHỈ các ID INPUT** đã được chèn với vị trí chính xác

## Tổng quan
Chức năng "Check quyền lợi" giúp bạn kiểm soát và tinh chỉnh vị trí các **ID input** đã được chèn vào.

## Cách hoạt động

### 1. Tự động tạo sheet
- Sau khi hoàn thành sắp xếp vị trí, chương trình sẽ **tự động** tạo sheet "Check quyền lợi"
- Sheet này có cấu trúc tương tự sheet Basket gốc (copy header từ dòng 1-3)

### 2. C<PERSON>u trúc dữ liệu ✅ **ĐÃ SỬA**
- **Từ dòng 4**: Hiển thị **CHỈ các ID INPUT** đã thêm vào liên tiếp
- **Cột ID**: Hiển thị các ID input đã được chèn (tương ứng với cột ID trên sheet Basket)
- **Cột vị trí**: Cột bên cạnh cột ID, hiển thị vị trí đã được chèn
- **🎯 Điểm quan trọng**: Chỉ hiển thị ID input, KHÔNG bao gồm ID đã có sẵn trong sheet

### 3. Ví dụ cụ thể
Giả sử bạn input các ID với điều kiện:
- **Top 150**: [123456, 234567] cho khung giờ 9h-10h
- **Top 200↓**: [345678, 456789] cho khung giờ 10h-11h  
- **Top 100**: [567890] cho khung giờ 11h-12h

**Kết quả trong sheet "Check quyền lợi":**
```
Dòng 1-3: Header (copy từ sheet Basket)

--- Time slot 9h-10h ---
Dòng 4: B4 = 123456, C4 = 2    (ID input tại vị trí 2)
Dòng 5: B5 = 234567, C5 = 4    (ID input tại vị trí 4)
Dòng 6: (trống - khoảng cách)

--- Time slot 10h-11h ---
Dòng 7: G7 = 345678, H7 = 3    (ID input tại vị trí 3)
Dòng 8: G8 = 456789, H8 = 5    (ID input tại vị trí 5)
Dòng 9: (trống - khoảng cách)

--- Time slot 11h-12h ---
Dòng 10: L10 = 567890, M10 = 2  (ID input tại vị trí 2)
```

### 4. Lợi ích ✅ **CẢI THIỆN**
- **Kiểm soát chính xác**: Chỉ xem ID input, không bị nhiễu bởi ID existing
- **Vị trí thực tế**: Hiển thị vị trí chính xác trong danh sách sau khi chèn
- **Tinh chỉnh dễ dàng**: Phát hiện ID input ở vị trí không mong muốn
- **Theo dõi liên tiếp**: ID input được hiển thị liên tiếp theo từng time slot

### 5. Lưu ý quan trọng
- Sheet "Check quyền lợi" sẽ được **tạo mới** mỗi lần chạy (xóa sheet cũ nếu có)
- Dữ liệu được sắp xếp theo thứ tự các time slot từ trái sang phải
- Có khoảng trống giữa các time slot để dễ phân biệt
- **CHỈ hiển thị ID input**, không hiển thị ID đã có sẵn

## Cách sử dụng

### Bước 1: Chạy chương trình
```bash
py basket_package.py
```

### Bước 2: Thực hiện các thao tác như thường lệ
- Load Google Sheet
- Load Deal list  
- Thiết lập điều kiện cho ID input
- Chạy xử lý

### Bước 3: Kiểm tra kết quả ✅ **MỚI**
- Sau khi hoàn thành, chương trình sẽ hiển thị: "Đã ghi các ID theo điều kiện đã chọn và tạo sheet Check quyền lợi"
- Mở Google Sheet và tìm sheet "Check quyền lợi"
- **Kiểm tra CHỈ các ID input** và vị trí đã được chèn

### Bước 4: Phân tích kết quả
Dựa vào thông tin trong sheet "Check quyền lợi", bạn có thể:
- **Xác minh vị trí**: ID input có ở đúng vị trí mong muốn không?
- **Phát hiện vấn đề**: ID nào ở vị trí quá cao/thấp?
- **Điều chỉnh điều kiện**: Thay đổi Top 150 thành Top 200↓ nếu cần
- **Ghi chú cần thiết**: Đánh dấu ID cần xem xét lại

## 🔍 **So sánh trước và sau khi sửa**

### ❌ **Trước khi sửa:**
```
Sheet "Check quyền lợi":
- Dòng 1-3: Header
- Từ dòng 4: (TRỐNG - không có dữ liệu)
```

### ✅ **Sau khi sửa:**
```
Sheet "Check quyền lợi":
- Dòng 1-3: Header
- Từ dòng 4: CHỈ ID input với vị trí chính xác
  B4 = 123456, C4 = 2
  B5 = 234567, C5 = 4
  G7 = 345678, H7 = 3
  ...
```

## Xử lý lỗi
Nếu gặp lỗi khi tạo sheet "Check quyền lợi":
- Kiểm tra quyền truy cập Google Sheet
- Đảm bảo không có sheet nào khác đang được chỉnh sửa
- Kiểm tra log để xem có ID input nào được tìm thấy không
- Thử chạy lại chương trình

## 🎯 **Kết luận**
Chức năng "Check quyền lợi" đã được **sửa lỗi hoàn toàn** và bây giờ:
- ✅ Hiển thị đầy đủ dữ liệu ID input và vị trí
- ✅ Phân biệt rõ ràng ID input vs ID existing  
- ✅ Cung cấp thông tin chính xác để kiểm soát và tinh chỉnh
- ✅ Hoạt động ổn định và đáng tin cậy

Bạn có thể yên tâm sử dụng để kiểm soát vị trí các ID input một cách hiệu quả! 🚀
