# Hướng dẫn sử dụng chức năng "Check quyền lợi"

## Tổng quan
Chức năng "Check quyền lợi" đã được thêm vào chương trình basket để giúp bạn kiểm soát và tinh chỉnh vị trí các ID đã được chèn vào.

## Cách hoạt động

### 1. Tự động tạo sheet
- Sau khi hoàn thành sắp xếp vị trí, chương trình sẽ **tự động** tạo sheet "Check quyền lợi"
- Sheet này có cấu trúc tương tự sheet Basket gốc (copy header từ dòng 1-3)

### 2. Cấu trúc dữ liệu
- **Từ dòng 4**: Hi<PERSON><PERSON> thị các ID đã thêm vào liên tiếp
- **Cột ID**: <PERSON><PERSON><PERSON> thị các ID đã đượ<PERSON> chèn (tương ứng với cột ID trên sheet Basket)
- **Cột vị trí**: <PERSON><PERSON><PERSON> bên cạnh cột ID, hiển thị vị trí đã được chèn

### 3. Ví dụ cụ thể
Giả sử bạn có 3 khung giờ:
- **9h-10h** (cột B): ID ở cột B, vị trí ở cột C
- **10h-11h** (cột G): ID ở cột G, vị trí ở cột H  
- **11h-12h** (cột L): ID ở cột L, vị trí ở cột M

Kết quả trong sheet "Check quyền lợi":
```
Dòng 4: B4 = 123456, C4 = 1
Dòng 5: B5 = 234567, C5 = 2
Dòng 6: B6 = 345678, C6 = 4
Dòng 7: (trống - khoảng cách giữa các time slot)
Dòng 8: G8 = 456789, H8 = 1
Dòng 9: G9 = 567890, H9 = 3
...
```

### 4. Lợi ích
- **Kiểm soát vị trí**: Xem chính xác ID nào được chèn ở vị trí nào
- **Tinh chỉnh dễ dàng**: Có thể điều chỉnh vị trí nếu phát hiện sai sót
- **Theo dõi liên tiếp**: Các ID được hiển thị liên tiếp bất kể vị trí thực tế

### 5. Lưu ý quan trọng
- Sheet "Check quyền lợi" sẽ được **tạo mới** mỗi lần chạy (xóa sheet cũ nếu có)
- Dữ liệu được sắp xếp theo thứ tự các time slot từ trái sang phải
- Có khoảng trống giữa các time slot để dễ phân biệt

## Cách sử dụng

### Bước 1: Chạy chương trình bình thường
```bash
py basket_package.py
```

### Bước 2: Thực hiện các thao tác như thường lệ
- Load Google Sheet
- Load Deal list  
- Thiết lập điều kiện
- Chạy xử lý

### Bước 3: Kiểm tra kết quả
- Sau khi hoàn thành, chương trình sẽ hiển thị thông báo: "Đã ghi các ID theo điều kiện đã chọn và tạo sheet Check quyền lợi"
- Mở Google Sheet và tìm sheet "Check quyền lợi"
- Kiểm tra các ID và vị trí đã được chèn

### Bước 4: Tinh chỉnh (nếu cần)
- Dựa vào thông tin trong sheet "Check quyền lợi", bạn có thể:
  - Điều chỉnh điều kiện và chạy lại
  - Thủ công chỉnh sửa vị trí trên sheet Basket
  - Ghi chú các vấn đề cần xử lý

## Xử lý lỗi
Nếu gặp lỗi khi tạo sheet "Check quyền lợi":
- Kiểm tra quyền truy cập Google Sheet
- Đảm bảo không có sheet nào khác đang được chỉnh sửa
- Thử chạy lại chương trình

## Kết luận
Chức năng "Check quyền lợi" giúp bạn có cái nhìn tổng quan về các ID đã được chèn và vị trí của chúng, từ đó có thể kiểm soát và tinh chỉnh kết quả một cách hiệu quả.
